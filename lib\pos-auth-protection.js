/**
 * POS Authentication Protection System
 * 
 * This module provides specialized authentication protection for POS operations
 * to prevent session clearing during legitimate payment processing activities.
 */

let posOperationActive = false;
let posSessionProtectionEnabled = false;

/**
 * Enable POS session protection
 * Call this when starting POS operations to prevent auth recovery interference
 */
export function enablePOSSessionProtection() {
  posSessionProtectionEnabled = true;
  posOperationActive = true;
  
  // Set a flag in sessionStorage that the auth recovery script can check
  if (typeof window !== 'undefined') {
    sessionStorage.setItem('pos_operation_active', 'true');
    sessionStorage.setItem('pos_session_protected', Date.now().toString());
    console.log('🛡️ POS session protection enabled');
  }
}

/**
 * Disable POS session protection
 * Call this when POS operations are complete
 */
export function disablePOSSessionProtection() {
  posSessionProtectionEnabled = false;
  posOperationActive = false;
  
  if (typeof window !== 'undefined') {
    sessionStorage.removeItem('pos_operation_active');
    sessionStorage.removeItem('pos_session_protected');
    console.log('🛡️ POS session protection disabled');
  }
}

/**
 * Check if POS session protection is currently active
 */
export function isPOSSessionProtected() {
  if (typeof window === 'undefined') return false;
  
  const isProtected = sessionStorage.getItem('pos_operation_active') === 'true';
  const protectionTime = sessionStorage.getItem('pos_session_protected');
  
  // Auto-disable protection after 10 minutes to prevent permanent protection
  if (isProtected && protectionTime) {
    const elapsed = Date.now() - parseInt(protectionTime);
    if (elapsed > 10 * 60 * 1000) { // 10 minutes
      console.log('🛡️ POS session protection auto-expired after 10 minutes');
      disablePOSSessionProtection();
      return false;
    }
  }
  
  return isProtected;
}

/**
 * Mark the start of a POS payment operation
 * This provides extra protection during critical payment moments
 */
export function startPOSPaymentOperation() {
  enablePOSSessionProtection();
  
  if (typeof window !== 'undefined') {
    sessionStorage.setItem('pos_payment_in_progress', 'true');
    sessionStorage.setItem('pos_payment_start_time', Date.now().toString());
    console.log('💳 POS payment operation started - enhanced protection active');
  }
}

/**
 * Mark the end of a POS payment operation
 */
export function endPOSPaymentOperation() {
  if (typeof window !== 'undefined') {
    sessionStorage.removeItem('pos_payment_in_progress');
    sessionStorage.removeItem('pos_payment_start_time');
    console.log('💳 POS payment operation completed');
  }
  
  // Keep general POS protection active but remove payment-specific protection
  // The general protection will be disabled when leaving the POS page
}

/**
 * Check if a payment operation is currently in progress
 */
export function isPOSPaymentInProgress() {
  if (typeof window === 'undefined') return false;
  
  const inProgress = sessionStorage.getItem('pos_payment_in_progress') === 'true';
  const startTime = sessionStorage.getItem('pos_payment_start_time');
  
  // Auto-clear payment protection after 5 minutes
  if (inProgress && startTime) {
    const elapsed = Date.now() - parseInt(startTime);
    if (elapsed > 5 * 60 * 1000) { // 5 minutes
      console.log('💳 POS payment protection auto-expired after 5 minutes');
      endPOSPaymentOperation();
      return false;
    }
  }
  
  return inProgress;
}

/**
 * Enhanced authentication token retrieval for POS operations
 * This version is more resilient and doesn't trigger auth recovery
 */
export async function getPOSAuthToken() {
  try {
    console.log('🔐 Getting POS auth token...');
    
    // Enable protection during token retrieval
    const wasProtected = isPOSSessionProtected();
    if (!wasProtected) {
      enablePOSSessionProtection();
    }
    
    // Import auth token manager
    const { getAuthToken } = await import('./auth-token-manager');
    const token = await getAuthToken();
    
    if (!token) {
      console.warn('🔐 No auth token available for POS operation');
      throw new Error('Authentication required for POS operations');
    }
    
    console.log('🔐 POS auth token retrieved successfully');
    return token;
    
  } catch (error) {
    console.error('🔐 Error getting POS auth token:', error);
    throw error;
  }
}

/**
 * POS-safe session validation
 * This version won't trigger auth recovery during validation
 */
export async function validatePOSSession() {
  try {
    console.log('🔍 Validating POS session...');
    
    // Enable protection during validation
    enablePOSSessionProtection();
    
    // Import Supabase client
    const { supabase } = await import('./supabase');
    
    // Get current session without forcing refresh initially
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.warn('🔍 Session validation error:', error.message);
      throw new Error(`Session validation failed: ${error.message}`);
    }
    
    if (!session) {
      console.warn('🔍 No active session found');
      throw new Error('No active session found. Please log in again.');
    }
    
    console.log('🔍 POS session validated successfully');
    return {
      session,
      user: session.user,
      isValid: true
    };
    
  } catch (error) {
    console.error('🔍 POS session validation failed:', error);
    throw error;
  }
}

/**
 * Initialize POS authentication protection
 * Call this when entering POS pages
 */
export function initializePOSAuthProtection() {
  console.log('🛡️ Initializing POS authentication protection...');
  
  enablePOSSessionProtection();
  
  // Set up cleanup when leaving POS pages
  if (typeof window !== 'undefined') {
    const handleBeforeUnload = () => {
      disablePOSSessionProtection();
    };
    
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Page is hidden, but don't disable protection immediately
        // in case user is just switching tabs during payment
        console.log('🛡️ Page hidden, maintaining POS protection');
      }
    };
    
    window.addEventListener('beforeunload', handleBeforeUnload);
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // Cleanup function
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      disablePOSSessionProtection();
    };
  }
  
  return () => disablePOSSessionProtection();
}

/**
 * Global flag for other systems to check POS protection status
 */
if (typeof window !== 'undefined') {
  window.isPOSSessionProtected = isPOSSessionProtected;
  window.isPOSPaymentInProgress = isPOSPaymentInProgress;
}
