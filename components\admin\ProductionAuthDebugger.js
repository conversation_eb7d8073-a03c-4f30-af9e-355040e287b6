/**
 * Production Authentication Debugger
 * 
 * This component provides enhanced debugging specifically for production mode
 * to help identify authentication issues that only occur in built applications.
 */

import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/router';

const ProductionAuthDebugger = () => {
  const { user, error, loading } = useAuth();
  const router = useRouter();
  const [debugInfo, setDebugInfo] = useState({});
  const [isVisible, setIsVisible] = useState(false);
  const [clientInstances, setClientInstances] = useState(0);

  // Collect comprehensive debug information
  useEffect(() => {
    const collectDebugInfo = () => {
      const info = {
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
        pathname: router.pathname,
        asPath: router.asPath,
        userAgent: navigator.userAgent,
        
        // Authentication state
        user: user ? {
          id: user.id,
          email: user.email,
          created_at: user.created_at
        } : null,
        loading,
        error: error ? error.message : null,
        
        // Supabase client information
        supabaseClientCount: window.__SUPABASE_CLIENT_COUNT || 0,
        supabaseClientExists: !!window.__SUPABASE_CLIENT_INSTANCE,
        
        // Storage information
        sessionStorage: {
          authState: !!sessionStorage.getItem('oss_auth_state'),
          tokenCache: !!sessionStorage.getItem('oss_auth_token_cache'),
          redirecting: !!sessionStorage.getItem('auth_redirecting'),
          redirectAfterLogin: sessionStorage.getItem('redirect_after_login')
        },
        
        localStorage: {
          authToken: !!localStorage.getItem('oss_auth_token'),
          supabaseAuth: Object.keys(localStorage).filter(key => key.includes('supabase')).length
        },
        
        cookies: {
          authToken: document.cookie.includes('oss_auth_token'),
          supabaseCookies: document.cookie.split(';').filter(cookie => cookie.includes('supabase')).length
        },
        
        // Performance information
        memoryUsage: performance.memory ? {
          used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
          total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
          limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
        } : null
      };
      
      setDebugInfo(info);
      setClientInstances(window.__SUPABASE_CLIENT_COUNT || 0);
    };

    collectDebugInfo();
    
    // Update debug info every 5 seconds
    const interval = setInterval(collectDebugInfo, 5000);
    
    return () => clearInterval(interval);
  }, [user, error, loading, router.pathname, router.asPath]);

  // Monitor for multiple client instances
  useEffect(() => {
    const checkClientInstances = () => {
      const count = window.__SUPABASE_CLIENT_COUNT || 0;
      if (count > 1) {
        console.warn(`[ProductionAuthDebugger] Multiple Supabase client instances detected: ${count}`);
        setIsVisible(true);
      }
    };

    checkClientInstances();
    const interval = setInterval(checkClientInstances, 2000);
    
    return () => clearInterval(interval);
  }, []);

  // Auto-show debugger when issues are detected
  useEffect(() => {
    if (error || clientInstances > 1) {
      setIsVisible(true);
    }
  }, [error, clientInstances]);

  // Export debug report
  const exportDebugReport = () => {
    const report = {
      ...debugInfo,
      exportedAt: new Date().toISOString(),
      url: window.location.href,
      referrer: document.referrer
    };
    
    console.log('🔍 Production Auth Debug Report:', JSON.stringify(report, null, 2));
    
    // Send to server for logging
    fetch('/api/admin/diagnostics/client-error', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        type: 'production_debug_report',
        message: 'Production authentication debug report',
        timestamp: report.exportedAt,
        userAgent: navigator.userAgent,
        url: window.location.href,
        debugData: report
      })
    }).catch(err => console.error('Failed to send debug report:', err));
    
    return report;
  };

  // Test authentication flow
  const testAuthFlow = async () => {
    console.log('🧪 Testing authentication flow...');
    
    try {
      // Test Supabase client access
      const { supabase } = await import('@/lib/supabase');
      console.log('✅ Supabase client imported successfully');
      
      // Test session retrieval
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      console.log('Session test:', sessionError ? '❌ Failed' : '✅ Success', sessionData?.session ? 'Has session' : 'No session');
      
      // Test token manager
      const { getAuthToken } = await import('@/lib/auth-token-manager');
      const token = await getAuthToken();
      console.log('Token test:', token ? '✅ Token retrieved' : '❌ No token');
      
      // Test API call
      const response = await fetch('/api/admin/diagnostics/auth-check', {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Content-Type': 'application/json'
        }
      });
      console.log('API test:', response.ok ? '✅ API call successful' : '❌ API call failed', response.status);
      
    } catch (error) {
      console.error('🚨 Auth flow test failed:', error);
    }
  };

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        style={{
          position: 'fixed',
          bottom: '10px',
          right: '10px',
          backgroundColor: clientInstances > 1 || error ? '#ff4444' : '#333',
          color: '#fff',
          border: 'none',
          borderRadius: '4px',
          padding: '8px 12px',
          fontSize: '12px',
          cursor: 'pointer',
          zIndex: 9999
        }}
      >
        🔍 Debug ({clientInstances > 1 ? `${clientInstances} clients` : 'OK'})
      </button>
    );
  }

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      left: '10px',
      width: '500px',
      maxHeight: '80vh',
      backgroundColor: '#1a1a1a',
      color: '#fff',
      border: '1px solid #444',
      borderRadius: '4px',
      padding: '15px',
      fontSize: '11px',
      fontFamily: 'monospace',
      zIndex: 10000,
      overflow: 'auto',
      boxShadow: '0 4px 8px rgba(0,0,0,0.3)'
    }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '15px',
        borderBottom: '1px solid #444',
        paddingBottom: '10px'
      }}>
        <strong style={{ color: '#4CAF50' }}>🔍 Production Auth Debugger</strong>
        <button
          onClick={() => setIsVisible(false)}
          style={{
            background: 'none',
            border: 'none',
            color: '#fff',
            cursor: 'pointer',
            fontSize: '16px'
          }}
        >
          ×
        </button>
      </div>

      {/* Status Overview */}
      <div style={{ marginBottom: '15px', padding: '10px', backgroundColor: '#2a2a2a', borderRadius: '4px' }}>
        <div style={{ color: clientInstances > 1 ? '#ff6666' : '#4CAF50', marginBottom: '5px' }}>
          <strong>Client Instances: {clientInstances}</strong>
          {clientInstances > 1 && ' ⚠️ MULTIPLE DETECTED'}
        </div>
        <div style={{ color: error ? '#ff6666' : '#4CAF50' }}>
          <strong>Auth Status: {error ? 'ERROR' : user ? 'AUTHENTICATED' : loading ? 'LOADING' : 'NOT AUTHENTICATED'}</strong>
        </div>
        <div style={{ fontSize: '10px', color: '#ccc', marginTop: '5px' }}>
          Path: {debugInfo.pathname} | Environment: {debugInfo.environment}
        </div>
      </div>

      {/* Quick Actions */}
      <div style={{ marginBottom: '15px' }}>
        <button
          onClick={testAuthFlow}
          style={{
            background: '#333',
            border: '1px solid #555',
            color: '#fff',
            padding: '5px 10px',
            borderRadius: '3px',
            cursor: 'pointer',
            fontSize: '11px',
            marginRight: '10px'
          }}
        >
          🧪 Test Auth Flow
        </button>
        <button
          onClick={exportDebugReport}
          style={{
            background: '#333',
            border: '1px solid #555',
            color: '#fff',
            padding: '5px 10px',
            borderRadius: '3px',
            cursor: 'pointer',
            fontSize: '11px'
          }}
        >
          📋 Export Report
        </button>
      </div>

      {/* Debug Information */}
      <div style={{ fontSize: '10px' }}>
        <details style={{ marginBottom: '10px' }}>
          <summary style={{ cursor: 'pointer', color: '#4CAF50' }}>Storage Information</summary>
          <div style={{ marginLeft: '15px', marginTop: '5px' }}>
            <div>SessionStorage: {JSON.stringify(debugInfo.sessionStorage, null, 2)}</div>
            <div>LocalStorage: {JSON.stringify(debugInfo.localStorage, null, 2)}</div>
            <div>Cookies: {JSON.stringify(debugInfo.cookies, null, 2)}</div>
          </div>
        </details>

        <details style={{ marginBottom: '10px' }}>
          <summary style={{ cursor: 'pointer', color: '#4CAF50' }}>User Information</summary>
          <div style={{ marginLeft: '15px', marginTop: '5px' }}>
            {debugInfo.user ? JSON.stringify(debugInfo.user, null, 2) : 'No user data'}
          </div>
        </details>

        {debugInfo.memoryUsage && (
          <details style={{ marginBottom: '10px' }}>
            <summary style={{ cursor: 'pointer', color: '#4CAF50' }}>Memory Usage</summary>
            <div style={{ marginLeft: '15px', marginTop: '5px' }}>
              Used: {debugInfo.memoryUsage.used}MB / {debugInfo.memoryUsage.total}MB
              (Limit: {debugInfo.memoryUsage.limit}MB)
            </div>
          </details>
        )}
      </div>

      <div style={{
        marginTop: '15px',
        paddingTop: '10px',
        borderTop: '1px solid #444',
        fontSize: '9px',
        color: '#888'
      }}>
        Last updated: {new Date(debugInfo.timestamp).toLocaleTimeString()}
      </div>
    </div>
  );
};

export default ProductionAuthDebugger;
